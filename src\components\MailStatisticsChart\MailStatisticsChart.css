/* MailStatisticsChart Custom Styles */

.chart-bar-item {
  margin-bottom: 1rem;
}

.chart-bar-item:hover .progress-bar {
  opacity: 0.8;
  transform: scaleY(1.1);
  transition: all 0.3s ease;
}

.progress-bar {
  position: relative;
  overflow: visible;
}

.progress-bar:hover::after {
  content: attr(title);
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

.chart-container {
  padding: 1rem 0;
}

.chart-bars {
  margin: 1.5rem 0;
}

/* Grid lines for better readability */
.chart-grid {
  position: relative;
}

.chart-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 10% 100%;
  pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-bar-item .col-3 {
    font-size: 11px;
  }
  
  .chart-bar-item .col-2 {
    font-size: 12px;
  }
  
  .progress {
    height: 20px !important;
  }
}

/* Animation for bars */
@keyframes slideIn {
  from {
    width: 0%;
  }
  to {
    width: var(--target-width);
  }
}

.progress-bar {
  animation: slideIn 1s ease-out;
}
