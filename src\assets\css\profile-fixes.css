/* 
 * Profile Page CSS Fixes
 * Ensures avatar appears above header and proper styling
 */

/* Fix z-index for profile avatar to appear above header */
.card-profile-image {
  position: relative;
  z-index: 10 !important;
}

.card-profile-image img {
  max-width: 180px;
  border-radius: 0.375rem;
  transform: translate(-50%, -30%);
  position: absolute;
  left: 50%;
  transition: all 0.15s ease;
  z-index: 15 !important;
  box-shadow: 0 0 2rem 0 rgba(136, 152, 170, 0.15) !important;
}

.card-profile-image img:hover {
  transform: translate(-50%, -33%);
}

/* Ensure profile card has proper stacking context */
.card-profile {
  position: relative;
  z-index: 5;
}

/* Profile header styling improvements */
.profile-header {
  background: linear-gradient(135deg, #f8f9fe 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments for profile avatar */
@media (max-width: 767px) {
  .card-profile-image img {
    max-width: 120px;
    transform: translate(-50%, -25%);
  }
  
  .card-profile-image img:hover {
    transform: translate(-50%, -28%);
  }
}

/* Ensure proper spacing and layout */
.profile-content {
  position: relative;
  z-index: 2;
}
