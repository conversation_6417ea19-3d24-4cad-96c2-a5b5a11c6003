/*!

=========================================================
* Argon Dashboard React - v1.2.4
=========================================================

* Product Page: https://www.creative-tim.com/product/argon-dashboard-react
* Copyright 2024 Creative Tim (https://www.creative-tim.com)
* Licensed under MIT (https://github.com/creativetimofficial/argon-dashboard-react/blob/master/LICENSE.md)

* Coded by Creative Tim

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

*/

// reactstrap components
import { Container } from "reactstrap";

const SimpleHeader = () => {
  return (
    <>
      <div className="header bg-gradient-info pb-8 pt-5 pt-md-8" style={{ position: 'relative', zIndex: 1 }}>
        <Container fluid>
          <div className="header-body">
            {/* Empty header body - just for spacing and background */}
          </div>
        </Container>
      </div>
    </>
  );
};

export default SimpleHeader;
