<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact Header Test</title>
    <style>
        /* Test styles for compact header */
        .header {
            background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
            color: white;
            position: relative;
            z-index: 1;
        }
        
        .header.compact {
            min-height: 80px;
            padding-bottom: 1rem;
            padding-top: 0.5rem;
        }
        
        @media (min-width: 768px) {
            .header.compact {
                min-height: 100px;
                padding-bottom: 1.5rem;
                padding-top: 1rem;
            }
        }
        
        @media (max-width: 767px) {
            .header.compact {
                min-height: 60px;
                padding-bottom: 0.75rem;
                padding-top: 0.5rem;
            }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .compact-layout {
            margin-top: -2rem;
            position: relative;
            z-index: 2;
        }
        
        @media (max-width: 767px) {
            .compact-layout {
                margin-top: -1.5rem;
            }
        }
        
        .nav-tabs {
            background: white;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 1rem;
            position: relative;
            z-index: 3;
        }
        
        .nav-tab {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
            cursor: pointer;
        }
        
        .nav-tab.active {
            background: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        
        .card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
        }
        
        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            position: relative;
            z-index: 2;
        }
        
        .card-body {
            padding: 1rem;
        }
        
        .test-content {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="header compact">
        <div class="container">
            <div class="header-body">
                <!-- Empty header body for minimal spacing -->
            </div>
        </div>
    </div>
    
    <div class="container compact-layout">
        <h1>Compact Header Test</h1>
        
        <div class="nav-tabs">
            <div class="nav-tab active">Groups</div>
            <div class="nav-tab">PICs</div>
            <div class="nav-tab">Users</div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>Test Card Header</h3>
                <p>This should not be overlapped by the header</p>
            </div>
            <div class="card-body">
                <div class="test-content">
                    <p>Test content area - should be properly spaced</p>
                </div>
                
                <h4>Header Height Test</h4>
                <ul>
                    <li><strong>Desktop (≥768px):</strong> 100px min-height</li>
                    <li><strong>Mobile (<768px):</strong> 60px min-height</li>
                    <li><strong>Container margin:</strong> -2rem (desktop), -1.5rem (mobile)</li>
                </ul>
                
                <h4>Z-Index Layers</h4>
                <ul>
                    <li><strong>Header:</strong> z-index: 1</li>
                    <li><strong>Container:</strong> z-index: 2</li>
                    <li><strong>Navigation:</strong> z-index: 3</li>
                </ul>
                
                <h4>Visual Check</h4>
                <p>✅ Header should not overlap navigation tabs</p>
                <p>✅ Card header should be properly positioned</p>
                <p>✅ Content should have adequate spacing</p>
                <p>✅ Responsive design should work on mobile</p>
            </div>
        </div>
    </div>
    
    <script>
        // Test responsive behavior
        function checkHeaderHeight() {
            const header = document.querySelector('.header.compact');
            const container = document.querySelector('.compact-layout');
            
            console.log('Header height:', header.offsetHeight + 'px');
            console.log('Container margin-top:', getComputedStyle(container).marginTop);
            console.log('Window width:', window.innerWidth + 'px');
        }
        
        // Check on load and resize
        window.addEventListener('load', checkHeaderHeight);
        window.addEventListener('resize', checkHeaderHeight);
        
        // Tab switching test
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
