/**
 * Test script for Move to Review functionality
 * This script tests the move-to-review API endpoint and functionality
 */

const http = require('http');

// Test data
const testMail = {
  id: "test-mail-001",
  Subject: "Test Mail for Review",
  From: "<EMAIL>",
  Type: "To",
  Date: ["2025-01-13", "14:30"],
  SummaryContent: "This is a test mail to check move to review functionality",
  Body: "<p>Test mail content for move to review testing</p>",
  isRead: false,
  category: "ValidMail",
  status: "valid"
};

// Function to make HTTP request
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test functions
async function testServerConnection() {
  console.log('🔍 Testing server connection...');
  
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/mail-stats',
      method: 'GET'
    };
    
    const result = await makeRequest(options);
    
    if (result.status === 200) {
      console.log('✅ Server is running');
      console.log('📊 Mail stats:', result.data);
      return true;
    } else {
      console.log('❌ Server responded with status:', result.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    return false;
  }
}

async function testMoveToReview() {
  console.log('\n🧪 Testing Move to Review functionality...');
  
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/move-to-review',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const requestData = {
      mailId: testMail.id,
      mailData: testMail
    };
    
    console.log('📤 Sending request to move mail to review...');
    console.log('📧 Mail:', testMail.Subject);
    
    const result = await makeRequest(options, requestData);
    
    console.log('📥 Response status:', result.status);
    console.log('📥 Response data:', result.data);
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ Move to Review successful!');
      return true;
    } else {
      console.log('❌ Move to Review failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Move to Review error:', error.message);
    return false;
  }
}

async function testMoveBack() {
  console.log('\n🔄 Testing Move Back functionality...');
  
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/move-back-from-review',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const reviewMail = {
      ...testMail,
      category: "ReviewMail",
      status: "review",
      originalCategory: "ValidMail",
      originalStatus: "valid",
      movedToReviewAt: new Date().toISOString()
    };
    
    const requestData = {
      mailId: reviewMail.id,
      mailData: reviewMail
    };
    
    console.log('📤 Sending request to move mail back...');
    
    const result = await makeRequest(options, requestData);
    
    console.log('📥 Response status:', result.status);
    console.log('📥 Response data:', result.data);
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ Move Back successful!');
      return true;
    } else {
      console.log('❌ Move Back failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Move Back error:', error.message);
    return false;
  }
}

async function checkReviewMails() {
  console.log('\n📁 Checking ReviewMail folder...');
  
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/mails',
      method: 'GET'
    };
    
    const result = await makeRequest(options);
    
    if (result.status === 200) {
      const reviewMails = result.data.filter(mail => mail.category === 'ReviewMail');
      console.log(`📧 Found ${reviewMails.length} mails in ReviewMail folder`);
      
      if (reviewMails.length > 0) {
        console.log('📋 ReviewMail contents:');
        reviewMails.forEach((mail, index) => {
          console.log(`   ${index + 1}. ${mail.Subject} (from: ${mail.From})`);
        });
      }
      
      return reviewMails;
    } else {
      console.log('❌ Failed to get mails data');
      return [];
    }
  } catch (error) {
    console.log('❌ Error checking ReviewMails:', error.message);
    return [];
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Move to Review Tests');
  console.log('================================');
  
  // Test 1: Server connection
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('\n❌ Cannot proceed - server is not running');
    console.log('💡 Please start the mail server first: cd mail-server && node server.js');
    return;
  }
  
  // Test 2: Check current ReviewMails
  await checkReviewMails();
  
  // Test 3: Move to Review
  const moveOk = await testMoveToReview();
  
  // Test 4: Check ReviewMails after move
  if (moveOk) {
    console.log('\n📁 Checking ReviewMail folder after move...');
    await checkReviewMails();
  }
  
  // Test 5: Move Back (optional)
  if (moveOk) {
    await testMoveBack();
  }
  
  console.log('\n🎉 Tests completed!');
  console.log('\n💡 If tests fail, check:');
  console.log('   1. Mail server is running on port 3001');
  console.log('   2. C:\\classifyMail\\ folder exists');
  console.log('   3. ReviewMail folder has write permissions');
  console.log('   4. Frontend is calling the correct API endpoint');
}

// Run tests
runTests().catch(console.error);
