/* Server Full Width Styles */

/* Make server page use full width */
.server-fullwidth {
  width: 100% !important;
  max-width: none !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Optimize RealtimeMailMonitor for full width */
.server-fullwidth .card {
  width: 100%;
  margin: 0;
}

.server-fullwidth .card-body {
  padding: 1.5rem;
}

/* Improve responsive layout for server monitoring */
@media (min-width: 1200px) {
  .server-fullwidth {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
  
  .server-fullwidth .card-body {
    padding: 2rem;
  }
  
  /* Make statistics section use more space on large screens */
  .server-fullwidth .mail-stats-section {
    display: flex;
    gap: 2rem;
  }
  
  .server-fullwidth .mail-stats-section > div {
    flex: 1;
  }
}

@media (min-width: 1400px) {
  .server-fullwidth {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
}

/* Optimize control actions layout */
.server-fullwidth .control-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.server-fullwidth .control-actions .btn {
  flex: 0 0 auto;
  min-width: 120px;
}

/* Improve simulate mail form layout */
.server-fullwidth .simulate-form {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #dee2e6;
}

.server-fullwidth .simulate-form .row {
  align-items: end;
}

/* Better spacing for server health section */
.server-fullwidth .server-health {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #28a745;
}

.server-fullwidth .server-health.error {
  border-left-color: #dc3545;
  background: #f8d7da;
}

.server-fullwidth .server-health.warning {
  border-left-color: #ffc107;
  background: #fff3cd;
}

/* Improve table styling */
.server-fullwidth .table {
  margin-bottom: 0;
}

.server-fullwidth .table td {
  padding: 0.5rem 0.75rem;
  border-top: 1px solid #dee2e6;
}

.server-fullwidth .table td:first-child {
  font-weight: 500;
  color: #495057;
  width: 40%;
}

/* Progress bar improvements */
.server-fullwidth .progress {
  height: 0.5rem;
  margin-top: 0.25rem;
}

/* Badge improvements */
.server-fullwidth .badge {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* Alert improvements */
.server-fullwidth .alert {
  border-radius: 0.5rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Card header improvements */
.server-fullwidth .card-header {
  background: linear-gradient(87deg, #f8f9fa 0, #ffffff 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.server-fullwidth .card-header h3 {
  color: #32325d;
  font-weight: 600;
}

.server-fullwidth .card-header p {
  color: #8898aa;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .server-fullwidth {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
  
  .server-fullwidth .card-body {
    padding: 1rem;
  }
  
  .server-fullwidth .card-header {
    padding: 1rem;
  }
  
  .server-fullwidth .control-actions {
    gap: 0.5rem;
  }
  
  .server-fullwidth .control-actions .btn {
    min-width: 100px;
    font-size: 0.875rem;
  }
  
  .server-fullwidth .simulate-form {
    padding: 1rem;
  }
}

/* Animation for smooth transitions */
.server-fullwidth .card {
  transition: all 0.3s ease;
}

.server-fullwidth .btn {
  transition: all 0.2s ease;
}

.server-fullwidth .alert {
  transition: all 0.3s ease;
}
