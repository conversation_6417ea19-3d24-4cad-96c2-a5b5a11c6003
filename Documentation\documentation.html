<!--
/*!

=========================================================
* Argon Dashboard React - v1.2.4
=========================================================

* Product Page: https://www.creative-tim.com/product/argon-dashboard-react
* Copyright 2024 Creative Tim (https://www.creative-tim.com)
* Licensed under MIT (https://github.com/creativetimofficial/argon-dashboard-react/blob/master/LICENSE.md)

* Coded by Creative Tim

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

*/
-->
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta
      name="description"
      content="Start your development with a Dashboard for Bootstrap 4."
    />
    <meta name="author" content="Creative Tim" />
    <title>Argon Dashboard - Free Dashboard for Bootstrap 4</title>
    <!-- Favicon -->
    <link href="../public/apple-icon.png" rel="icon" type="image/png" />
    <!-- Fonts -->
    <link
      href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"
      rel="stylesheet"
    />
    <!-- Icons -->
    <link href="../src/assets/vendor/nucleo/css/nucleo.css" rel="stylesheet" />
    <link
      href="../src/assets/vendor/@fortawesome/fontawesome-free/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Argon CSS -->
    <link
      type="text/css"
      href="../src/assets/css/argon-dashboard-react.css?v=1.2.1"
      rel="stylesheet"
    />
  </head>

  <body>
    <!-- Main content -->
    <div class="main-content">
      <!-- Top navbar -->
      <nav
        class="navbar navbar-top navbar-expand-md navbar-dark"
        id="navbar-main"
      >
        <div class="container-fluid">
          <!-- Brand -->
          <a
            class="h4 mb-0 text-white text-uppercase d-none d-lg-inline-block"
            href="https://demos.creative-tim.com/argon-dashboard-react/#/documentation/overview"
            >Documentation</a
          >
          <!-- Form -->
          <form
            class="navbar-search navbar-search-dark form-inline mr-3 d-none d-md-flex ml-lg-auto"
          >
            <div class="form-group mb-0">
              <div class="input-group input-group-alternative">
                <div class="input-group-prepend">
                  <span class="input-group-text"
                    ><i class="fas fa-search"></i
                  ></span>
                </div>
                <input class="form-control" placeholder="Search" type="text" />
              </div>
            </div>
          </form>
          <!-- User -->
          <ul class="navbar-nav align-items-center d-none d-md-flex">
            <li class="nav-item dropdown">
              <a
                class="nav-link pr-0"
                href="#"
                role="button"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <div class="media align-items-center">
                  <span class="avatar avatar-sm rounded-circle">
                    <img
                      alt="Image placeholder"
                      src="../src/assets/img/theme/team-4-800x800.jpg"
                    />
                  </span>
                  <div class="media-body ml-2 d-none d-lg-block">
                    <span class="mb-0 text-sm font-weight-bold"
                      >Jessica Jones</span
                    >
                  </div>
                </div>
              </a>
              <div
                class="dropdown-menu dropdown-menu-arrow dropdown-menu-right"
              >
                <div class="dropdown-header noti-title">
                  <h6 class="text-overflow m-0">Welcome!</h6>
                </div>
                <a href="#pablo" class="dropdown-item">
                  <i class="ni ni-single-02"></i> <span>My profile</span>
                </a>
                <a href="#pablo" class="dropdown-item">
                  <i class="ni ni-settings-gear-65"></i> <span>Settings</span>
                </a>
                <a href="#pablo" class="dropdown-item">
                  <i class="ni ni-calendar-grid-58"></i> <span>Activity</span>
                </a>
                <a href="#pablo" class="dropdown-item">
                  <i class="ni ni-support-16"></i> <span>Support</span>
                </a>
                <div class="dropdown-divider"></div>
                <a href="#pablo" class="dropdown-item">
                  <i class="ni ni-user-run"></i> <span>Logout</span>
                </a>
              </div>
            </li>
          </ul>
        </div>
      </nav>
      <!-- Header -->
      <div
        class="header bg-gradient-primary pb-8 pt-5 pt-lg-8 d-flex align-items-center"
      >
        <!-- Header container -->
        <div class="container-fluid">
          <div class="row justify-content-center">
            <div class="col-lg-6 col-md-10 text-center">
              <h1 class="display-2 text-white">
                Documentation <small>v1.2.1</small>
              </h1>
              <p class="text-white mt-0 mb-5">
                We are constatly doing updates on the product and documentation,
                so please check the online version.
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Page content -->
      <div class="container-fluid mt--7">
        <div class="row justify-content-center">
          <div class="col-lg-4">
            <div class="card bg-gradient-default text-center p-5">
              <a
                href="https://demos.creative-tim.com/argon-dashboard-react/#/documentation/overview"
                class="btn btn-white btn-icon mb-3 mb-sm-0"
              >
                <span class="btn-inner--icon"
                  ><i class="ni ni-collection"></i
                ></span>
                <span class="btn-inner--text">View Docs Online</span>
              </a>
            </div>
          </div>
        </div>
        <!-- Footer -->
      </div>
    </div>
    <!-- Argon Scripts -->
    <!-- Core -->
    <script
      src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
      integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
      crossorigin="anonymous"
    ></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"
      integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1"
      crossorigin="anonymous"
    ></script>
    <script
      src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"
      integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM"
      crossorigin="anonymous"
    ></script>
  </body>
</html>
