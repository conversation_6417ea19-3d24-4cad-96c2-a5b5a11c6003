/* Compact <PERSON><PERSON> Styles */

.header.compact {
  min-height: 120px !important;
  padding-bottom: 2rem !important;
  padding-top: 1rem !important;
}

@media (min-width: 768px) {
  .header.compact {
    padding-top: 1.5rem !important;
  }
}

.header.compact .header-body {
  padding-bottom: 0 !important;
}

.header.compact .header-body h6 {
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.header.compact .header-body p {
  font-size: 0.875rem;
  margin-bottom: 0;
  opacity: 0.8;
}

/* Adjust container margin for compact header */
.mail-page.compact-layout {
  margin-top: -3rem !important;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .header.compact {
    padding-bottom: 1.5rem !important;
    padding-top: 0.75rem !important;
  }
  
  .header.compact .header-body h6 {
    font-size: 1.125rem;
  }
  
  .mail-page.compact-layout {
    margin-top: -2.5rem !important;
  }
}

/* Animation for smooth transitions */
.header.compact {
  transition: all 0.3s ease;
}

.header.compact .header-body {
  transition: all 0.3s ease;
}
