import React, { useState, useEffect, useRef } from 'react';
import { Input, InputGroup, InputGroupAddon, Button } from 'reactstrap';

const DateInput = ({ value, onChange, placeholder = "dd/mm/yyyy", className, style, ...props }) => {
  const [displayValue, setDisplayValue] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const hiddenDateInputRef = useRef(null);

  // Convert yyyy-mm-dd to dd/mm/yyyy for display
  const formatToDisplay = (dateStr) => {
    if (!dateStr) return '';
    const [year, month, day] = dateStr.split('-');
    return `${day}/${month}/${year}`;
  };

  // Convert dd/mm/yyyy to yyyy-mm-dd for value
  const formatToValue = (displayStr) => {
    if (!displayStr) return '';

    // Remove any non-digit characters except /
    const cleaned = displayStr.replace(/[^\d/]/g, '');

    // Split by /
    const parts = cleaned.split('/');

    if (parts.length === 3) {
      const [day, month, year] = parts;

      // Validate parts
      const dayNum = parseInt(day, 10);
      const monthNum = parseInt(month, 10);
      const yearNum = parseInt(year, 10);

      if (dayNum >= 1 && dayNum <= 31 &&
          monthNum >= 1 && monthNum <= 12 &&
          yearNum >= 1900 && yearNum <= 2100) {

        // Pad with zeros
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');

        return `${yearNum}-${paddedMonth}-${paddedDay}`;
      }
    }

    return '';
  };

  // Update display value when prop value changes
  useEffect(() => {
    setDisplayValue(formatToDisplay(value));
  }, [value]);

  // Handle input change
  const handleChange = (e) => {
    let inputValue = e.target.value;
    
    // Auto-format as user types
    inputValue = inputValue.replace(/[^\d]/g, ''); // Remove non-digits
    
    if (inputValue.length >= 2) {
      inputValue = inputValue.substring(0, 2) + '/' + inputValue.substring(2);
    }
    if (inputValue.length >= 5) {
      inputValue = inputValue.substring(0, 5) + '/' + inputValue.substring(5, 9);
    }
    
    setDisplayValue(inputValue);
    
    // Convert to yyyy-mm-dd and call onChange
    const convertedValue = formatToValue(inputValue);
    if (onChange) {
      onChange({
        ...e,
        target: {
          ...e.target,
          value: convertedValue
        }
      });
    }
  };

  // Handle blur to validate and format
  const handleBlur = (e) => {
    const convertedValue = formatToValue(displayValue);
    if (convertedValue) {
      setDisplayValue(formatToDisplay(convertedValue));
    } else if (displayValue) {
      // Invalid date, clear it
      setDisplayValue('');
      if (onChange) {
        onChange({
          ...e,
          target: {
            ...e.target,
            value: ''
          }
        });
      }
    }
  };

  // Handle date picker change
  const handleDatePickerChange = (e) => {
    const selectedDate = e.target.value; // yyyy-mm-dd format
    setDisplayValue(formatToDisplay(selectedDate));

    if (onChange) {
      onChange({
        ...e,
        target: {
          ...e.target,
          value: selectedDate
        }
      });
    }
  };

  // Handle calendar icon click
  const handleCalendarClick = () => {
    if (hiddenDateInputRef.current) {
      hiddenDateInputRef.current.focus();
      hiddenDateInputRef.current.click();
    }
  };

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <InputGroup style={style}>
        <Input
          type="text"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={className}
          maxLength={10}
          {...props}
        />
        <InputGroupAddon addonType="append">
          <Button
            color="light"
            size="sm"
            onClick={handleCalendarClick}
            style={{
              border: '1px solid #cad1d7',
              borderLeft: 'none',
              padding: '0.25rem 0.5rem'
            }}
            type="button"
          >
            <i className="fas fa-calendar-alt" style={{ fontSize: '12px' }}></i>
          </Button>
        </InputGroupAddon>
      </InputGroup>

      {/* Hidden date input for native date picker */}
      <input
        ref={hiddenDateInputRef}
        type="date"
        value={value || ''}
        onChange={handleDatePickerChange}
        style={{
          position: 'absolute',
          left: '-9999px',
          opacity: 0,
          pointerEvents: 'none'
        }}
      />
    </div>
  );
};

export default DateInput;
