import React, { useState, useEffect } from 'react';
import { Input } from 'reactstrap';

const DateInput = ({ value, onChange, placeholder = "dd/mm/yyyy", className, style, ...props }) => {
  const [displayValue, setDisplayValue] = useState('');

  // Convert yyyy-mm-dd to dd/mm/yyyy for display
  const formatToDisplay = (dateStr) => {
    if (!dateStr) return '';
    const [year, month, day] = dateStr.split('-');
    return `${day}/${month}/${year}`;
  };

  // Convert dd/mm/yyyy to yyyy-mm-dd for value
  const formatToValue = (displayStr) => {
    if (!displayStr) return '';
    
    // Remove any non-digit characters except /
    const cleaned = displayStr.replace(/[^\d/]/g, '');
    
    // Split by /
    const parts = cleaned.split('/');
    
    if (parts.length === 3) {
      const [day, month, year] = parts;
      
      // Validate parts
      const dayNum = parseInt(day, 10);
      const monthNum = parseInt(month, 10);
      const yearNum = parseInt(year, 10);
      
      if (dayNum >= 1 && dayNum <= 31 && 
          monthNum >= 1 && monthNum <= 12 && 
          yearNum >= 1900 && yearNum <= 2100) {
        
        // Pad with zeros
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        
        return `${yearNum}-${paddedMonth}-${paddedDay}`;
      }
    }
    
    return '';
  };

  // Update display value when prop value changes
  useEffect(() => {
    setDisplayValue(formatToDisplay(value));
  }, [value]);

  // Handle input change
  const handleChange = (e) => {
    let inputValue = e.target.value;
    
    // Auto-format as user types
    inputValue = inputValue.replace(/[^\d]/g, ''); // Remove non-digits
    
    if (inputValue.length >= 2) {
      inputValue = inputValue.substring(0, 2) + '/' + inputValue.substring(2);
    }
    if (inputValue.length >= 5) {
      inputValue = inputValue.substring(0, 5) + '/' + inputValue.substring(5, 9);
    }
    
    setDisplayValue(inputValue);
    
    // Convert to yyyy-mm-dd and call onChange
    const convertedValue = formatToValue(inputValue);
    if (onChange) {
      onChange({
        ...e,
        target: {
          ...e.target,
          value: convertedValue
        }
      });
    }
  };

  // Handle blur to validate and format
  const handleBlur = (e) => {
    const convertedValue = formatToValue(displayValue);
    if (convertedValue) {
      setDisplayValue(formatToDisplay(convertedValue));
    } else if (displayValue) {
      // Invalid date, clear it
      setDisplayValue('');
      if (onChange) {
        onChange({
          ...e,
          target: {
            ...e.target,
            value: ''
          }
        });
      }
    }
  };

  return (
    <Input
      type="text"
      value={displayValue}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      className={className}
      style={style}
      maxLength={10}
      {...props}
    />
  );
};

export default DateInput;
