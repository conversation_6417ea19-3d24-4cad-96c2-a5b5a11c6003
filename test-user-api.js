/**
 * Test script for User Management API endpoints
 * This script tests all the user management API endpoints
 */

// Base URL for API
const API_BASE = 'http://localhost:3001';

// Test data
const testUser = {
  username: 'test.user.api',
  email: '<EMAIL>',
  fullName: 'Test API User',
  department: 'IT',
  phone: '+84-999-888-777',
  isAdmin: false,
  isActive: true
};

// Helper function to make API calls
const apiCall = async (endpoint, options = {}) => {
  const url = `${API_BASE}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    console.log(`🌐 ${finalOptions.method || 'GET'} ${url}`);
    if (finalOptions.body) {
      console.log('📤 Request body:', JSON.parse(finalOptions.body));
    }
    
    const response = await fetch(url, finalOptions);
    const data = await response.json();
    
    console.log(`📥 Response (${response.status}):`, data);
    console.log('');
    
    return { response, data };
  } catch (error) {
    console.error('❌ API call failed:', error);
    return { error };
  }
};

// Test functions
const testGetUsers = async () => {
  console.log('🧪 Testing GET /api/users');
  console.log('========================');
  
  const result = await apiCall('/api/users');
  
  if (result.response && result.response.ok) {
    console.log('✅ GET users successful');
    console.log(`📊 Found ${Array.isArray(result.data) ? result.data.length : 0} users`);
    return result.data;
  } else {
    console.log('❌ GET users failed');
    return [];
  }
};

const testCreateUser = async () => {
  console.log('🧪 Testing POST /api/users');
  console.log('===========================');
  
  const result = await apiCall('/api/users', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });
  
  if (result.response && result.response.ok) {
    console.log('✅ Create user successful');
    return result.data.user;
  } else {
    console.log('❌ Create user failed');
    return null;
  }
};

const testUpdateUser = async (userId) => {
  console.log('🧪 Testing PUT /api/users/:id');
  console.log('==============================');
  
  const updatedData = {
    ...testUser,
    fullName: 'Updated Test User',
    department: 'Marketing',
    phone: '+84-111-222-333'
  };
  
  const result = await apiCall(`/api/users/${userId}`, {
    method: 'PUT',
    body: JSON.stringify(updatedData)
  });
  
  if (result.response && result.response.ok) {
    console.log('✅ Update user successful');
    return result.data.user;
  } else {
    console.log('❌ Update user failed');
    return null;
  }
};

const testToggleAdmin = async (userId, currentAdminStatus) => {
  console.log('🧪 Testing PUT /api/users/:id/admin');
  console.log('====================================');
  
  const result = await apiCall(`/api/users/${userId}/admin`, {
    method: 'PUT',
    body: JSON.stringify({ isAdmin: !currentAdminStatus })
  });
  
  if (result.response && result.response.ok) {
    console.log('✅ Toggle admin successful');
    return result.data.user;
  } else {
    console.log('❌ Toggle admin failed');
    return null;
  }
};

const testToggleStatus = async (userId, currentActiveStatus) => {
  console.log('🧪 Testing PUT /api/users/:id/status');
  console.log('=====================================');
  
  const result = await apiCall(`/api/users/${userId}/status`, {
    method: 'PUT',
    body: JSON.stringify({ isActive: !currentActiveStatus })
  });
  
  if (result.response && result.response.ok) {
    console.log('✅ Toggle status successful');
    return result.data.user;
  } else {
    console.log('❌ Toggle status failed');
    return null;
  }
};

const testDeleteUser = async (userId) => {
  console.log('🧪 Testing DELETE /api/users/:id');
  console.log('=================================');
  
  const result = await apiCall(`/api/users/${userId}`, {
    method: 'DELETE'
  });
  
  if (result.response && result.response.ok) {
    console.log('✅ Delete user successful');
    return true;
  } else {
    console.log('❌ Delete user failed');
    return false;
  }
};

// Test validation errors
const testValidationErrors = async () => {
  console.log('🧪 Testing Validation Errors');
  console.log('=============================');
  
  // Test missing username
  console.log('📝 Testing missing username...');
  await apiCall('/api/users', {
    method: 'POST',
    body: JSON.stringify({ email: '<EMAIL>' })
  });
  
  // Test missing email
  console.log('📝 Testing missing email...');
  await apiCall('/api/users', {
    method: 'POST',
    body: JSON.stringify({ username: 'testuser' })
  });
  
  // Test duplicate username (if exists)
  console.log('📝 Testing duplicate username...');
  await apiCall('/api/users', {
    method: 'POST',
    body: JSON.stringify({ username: 'admin', email: '<EMAIL>' })
  });
};

// Run comprehensive test suite
const runAllTests = async () => {
  console.log('🚀 Starting User Management API Tests');
  console.log('======================================');
  console.log('');
  
  try {
    // 1. Get initial users
    const initialUsers = await testGetUsers();
    
    // 2. Test validation errors
    await testValidationErrors();
    
    // 3. Create new user
    const createdUser = await testCreateUser();
    if (!createdUser) {
      console.log('❌ Cannot continue tests - user creation failed');
      return;
    }
    
    // 4. Update user
    const updatedUser = await testUpdateUser(createdUser.id);
    
    // 5. Toggle admin status
    const adminToggled = await testToggleAdmin(createdUser.id, createdUser.isAdmin);
    
    // 6. Toggle active status
    const statusToggled = await testToggleStatus(createdUser.id, createdUser.isActive);
    
    // 7. Get users again to verify changes
    console.log('🔄 Verifying changes...');
    await testGetUsers();
    
    // 8. Delete test user
    const deleted = await testDeleteUser(createdUser.id);
    
    // 9. Final verification
    console.log('🔄 Final verification...');
    const finalUsers = await testGetUsers();
    
    console.log('✅ All tests completed!');
    console.log(`📊 Initial users: ${initialUsers.length}`);
    console.log(`📊 Final users: ${finalUsers.length}`);
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
};

// Test individual endpoints
const testEndpoints = {
  getUsers: testGetUsers,
  createUser: testCreateUser,
  updateUser: testUpdateUser,
  toggleAdmin: testToggleAdmin,
  toggleStatus: testToggleStatus,
  deleteUser: testDeleteUser,
  validationErrors: testValidationErrors,
  runAll: runAllTests
};

// Export for use in browser or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  window.testUserAPI = testEndpoints;
  console.log('🔧 User API Test Functions Loaded');
  console.log('==================================');
  console.log('Available functions:');
  console.log('- testUserAPI.getUsers()');
  console.log('- testUserAPI.createUser()');
  console.log('- testUserAPI.updateUser(userId)');
  console.log('- testUserAPI.toggleAdmin(userId, currentStatus)');
  console.log('- testUserAPI.toggleStatus(userId, currentStatus)');
  console.log('- testUserAPI.deleteUser(userId)');
  console.log('- testUserAPI.validationErrors()');
  console.log('- testUserAPI.runAll()');
  console.log('');
  console.log('💡 Run testUserAPI.runAll() to execute all tests');
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = testEndpoints;
  
  // Auto-run if called directly
  if (require.main === module) {
    runAllTests();
  }
}
