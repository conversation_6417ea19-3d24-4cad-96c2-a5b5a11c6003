/* Force dd/mm/yyyy format for date inputs */
.date-input-ddmmyyyy {
  position: relative;
}

.date-input-ddmmyyyy input[type="date"] {
  position: relative;
  color: transparent;
  background: white;
}

.date-input-ddmmyyyy input[type="date"]::-webkit-datetime-edit {
  color: transparent;
}

.date-input-ddmmyyyy input[type="date"]::-webkit-datetime-edit-text {
  color: transparent;
}

.date-input-ddmmyyyy input[type="date"]::-webkit-datetime-edit-month-field {
  color: transparent;
}

.date-input-ddmmyyyy input[type="date"]::-webkit-datetime-edit-day-field {
  color: transparent;
}

.date-input-ddmmyyyy input[type="date"]::-webkit-datetime-edit-year-field {
  color: transparent;
}

.date-input-ddmmyyyy input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #495057;
  cursor: pointer;
  z-index: 2;
}

.date-input-ddmmyyyy .date-display {
  position: absolute;
  top: 0;
  left: 0;
  right: 24px;
  bottom: 0;
  pointer-events: none;
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  color: #495057;
  background: transparent;
  z-index: 1;
}

.date-input-ddmmyyyy .date-display.placeholder {
  color: #6c757d;
}

/* For small form controls */
.date-input-ddmmyyyy.form-control-sm .date-display {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Focus styles */
.date-input-ddmmyyyy input[type="date"]:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Dark theme support for chart */
.bg-gradient-default .date-input-ddmmyyyy .date-display {
  color: #fff;
}

.bg-gradient-default .date-input-ddmmyyyy .date-display.placeholder {
  color: #adb5bd;
}

.bg-gradient-default .date-input-ddmmyyyy input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
}
